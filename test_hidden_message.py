#!/usr/bin/env python3
"""
测试隐藏空格用户消息功能的脚本
"""
import json
import sys
import importlib.util

# 导入修改后的函数
spec = importlib.util.spec_from_file_location("lmarena_local", "2lmarena_local.py")
lmarena_local = importlib.util.module_from_spec(spec)
spec.loader.exec_module(lmarena_local)

create_lmarena_request_body = lmarena_local.create_lmarena_request_body
MODEL_REGISTRY = lmarena_local.MODEL_REGISTRY

def test_hidden_space_message():
    """测试隐藏空格消息的插入"""
    print("🧪 测试隐藏空格用户消息功能...")
    
    # 设置测试模型
    MODEL_REGISTRY["test-model"] = {"id": "test-id", "type": "chat"}
    
    # 测试用例1：正常的用户-助手对话
    test_request_1 = {
        "model": "test-model",
        "messages": [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"},
            {"role": "user", "content": "How are you?"}
        ]
    }
    
    print("📝 测试用例1：正常对话（最后是用户消息）")
    payload_1, files_1 = create_lmarena_request_body(test_request_1)
    
    # 检查消息结构
    messages = payload_1["messages"]
    print(f"  总消息数: {len(messages)}")
    
    # 找到隐藏的空格消息
    space_message = None
    for msg in messages:
        if msg["role"] == "user" and msg["content"] == " ":
            space_message = msg
            break
    
    if space_message:
        print("  ✅ 找到隐藏的空格用户消息")
        print(f"  📍 空格消息内容: '{space_message['content']}'")
        print(f"  🔗 父消息ID: {space_message['parentMessageIds']}")
        
        # 验证父消息是最后一条真实用户消息
        last_real_user_msg = None
        for msg in messages:
            if msg["role"] == "user" and msg["content"] != " ":
                last_real_user_msg = msg
        
        if last_real_user_msg and space_message["parentMessageIds"][0] == last_real_user_msg["id"]:
            print("  ✅ 空格消息正确链接到最后一条真实用户消息")
        else:
            print("  ❌ 空格消息父消息链接错误")
            return False
    else:
        print("  ❌ 未找到隐藏的空格用户消息")
        return False
    
    # 验证消息顺序
    user_messages = [msg for msg in messages if msg["role"] == "user"]
    assistant_messages = [msg for msg in messages if msg["role"] == "assistant"]
    
    print(f"  👥 用户消息数: {len(user_messages)} (包含隐藏消息)")
    print(f"  🤖 助手消息数: {len(assistant_messages)}")
    
    # 验证最后的消息顺序：应该是 [真实用户消息] -> [隐藏空格消息] -> [待生成助手消息]
    last_three_messages = messages[-3:]
    if (len(last_three_messages) >= 3 and 
        last_three_messages[0]["role"] == "user" and last_three_messages[0]["content"] != " " and
        last_three_messages[1]["role"] == "user" and last_three_messages[1]["content"] == " " and
        last_three_messages[2]["role"] == "assistant"):
        print("  ✅ 消息顺序正确：[真实用户消息] -> [隐藏空格消息] -> [助手消息]")
    else:
        print("  ❌ 消息顺序不正确")
        print("  最后三条消息:")
        for i, msg in enumerate(last_three_messages):
            print(f"    {i+1}. {msg['role']}: '{msg['content']}'")
        return False
    
    return True

if __name__ == "__main__":
    success = test_hidden_space_message()
    if success:
        print("\n🎉 隐藏空格消息功能测试通过！")
    else:
        print("\n💥 隐藏空格消息功能测试失败！")
        sys.exit(1)
