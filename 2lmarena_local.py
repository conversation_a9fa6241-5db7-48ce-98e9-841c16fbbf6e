import asyncio
import json
import logging
import uuid
import re
import os
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request, HTTPException
from starlette.responses import StreamingResponse

# --- Configuration ---
logging.basicConfig(
    level=logging.DEBUG, # Set to DEBUG for maximum visibility
    format='%(asctime)s.%(msecs)03d - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
    datefmt='%H:%M:%S'
)
BACKPRESSURE_QUEUE_SIZE = 50 # Increased size slightly

# --- Model Registry ---
MODEL_REGISTRY = {}  # Will be populated dynamically
MODELS_JSON_FILE = "models.json"  # JSON file to store models

def save_models_to_json(models_data: dict) -> None:
    """Save models data to JSON file."""
    try:
        with open(MODELS_JSON_FILE, 'w', encoding='utf-8') as f:
            json.dump(models_data, f, indent=2, ensure_ascii=False)
        logging.info(f"Successfully saved {len(models_data)} models to {MODELS_JSON_FILE}")
    except Exception as e:
        logging.error(f"Failed to save models to JSON file: {e}", exc_info=True)

def load_models_from_json() -> dict:
    """Load models data from JSON file."""
    try:
        if os.path.exists(MODELS_JSON_FILE):
            with open(MODELS_JSON_FILE, 'r', encoding='utf-8') as f:
                models_data = json.load(f)
            logging.info(f"Successfully loaded {len(models_data)} models from {MODELS_JSON_FILE}")
            return models_data
        else:
            logging.info(f"Models JSON file {MODELS_JSON_FILE} does not exist")
            return {}
    except Exception as e:
        logging.error(f"Failed to load models from JSON file: {e}", exc_info=True)
        return {}

def update_model_registry(models_data: dict) -> None:
    """Update the model registry with data from browser, inferring type from capabilities."""
    global MODEL_REGISTRY
    
    try:
        if not models_data or not isinstance(models_data, dict):
            logging.warning(f"Received empty or invalid model data: {models_data}")
            return
        
        new_registry = {}
        for public_name, model_info in models_data.items():
            if not isinstance(model_info, dict):
                continue

            # Determine type from outputCapabilities
            model_type = "chat"  # Default
            capabilities = model_info.get("capabilities", {})
            if isinstance(capabilities, dict):
                output_caps = capabilities.get("outputCapabilities", {})
                if isinstance(output_caps, dict):
                    if "image" in output_caps:
                        model_type = "image"
                    elif "video" in output_caps:
                        model_type = "video"
            
            # Store the processed model info with the determined type
            processed_info = model_info.copy()
            processed_info["type"] = model_type
            new_registry[public_name] = processed_info

        MODEL_REGISTRY = new_registry
        logging.info(f"Updated and processed model registry with {len(MODEL_REGISTRY)} models.")

        # Save updated models to JSON file
        save_models_to_json(MODEL_REGISTRY)

    except Exception as e:
        logging.error(f"Error updating model registry: {e}", exc_info=True)

def get_fallback_registry():
    """Fallback registry in case dynamic fetching fails."""
    return {
        "wolfstride": {"id": "849f6833-8598-4329-b7a6-e765eb2a0c0d", "type": "chat"},
        "claude-3-5-sonnet-20241022": {"id": "f44e280a-7914-43ca-a25d-ecfcc5d48d09", "type": "chat"},
        "gemini-2.0-flash-001": {"id": "7a55108b-b997-4cff-a72f-5aa83beee918", "type": "chat"},
        "chatgpt-4o-latest-20250326": {"id": "9513524d-882e-4350-b31e-e4584440c2c8", "type": "chat"},
        "o3-2025-04-16": {"id": "cb0f1e24-e8e9-4745-aabc-b926ffde7475", "type": "chat"},
        "claude-opus-4-20250514": {"id": "ee116d12-64d6-48a8-88e5-b2d06325cdd2", "type": "chat"},
        "claude-sonnet-4-20250514": {"id": "ac44dd10-0666-451c-b824-386ccfea7bcc", "type": "chat"},
        "o3-mini": {"id": "c680645e-efac-4a81-b0af-da16902b2541", "type": "chat"},
    }

# --- Global State ---
browser_ws: WebSocket | None = None
response_channels: dict[str, asyncio.Queue] = {}

# --- FastAPI App and Lifespan ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    global MODEL_REGISTRY
    logging.info("Server starting up...")

    # Try to load models from JSON file first
    loaded_models = load_models_from_json()
    if loaded_models:
        MODEL_REGISTRY = loaded_models
        logging.info(f"Loaded {len(MODEL_REGISTRY)} models from JSON file")
    else:
        # Use fallback registry if JSON loading fails
        MODEL_REGISTRY = get_fallback_registry()
        logging.info(f"Loaded {len(MODEL_REGISTRY)} fallback models")

    logging.info("Server startup complete.")
    yield
    logging.info("Server shutting down.")

app = FastAPI(lifespan=lifespan)

# --- WebSocket Handler (Producer) ---
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    global browser_ws
    await websocket.accept()
    logging.info("✅ WebSocket from browser connected.")
    browser_ws = websocket
    try:
        while True:
            message_str = await websocket.receive_text()
            message = json.loads(message_str)
            
            # Handle model registry updates
            if message.get("type") == "model_registry":
                models_data = message.get("models", {})
                update_model_registry(models_data)
                
                # Send acknowledgment
                await websocket.send_text(json.dumps({
                    "type": "model_registry_ack",
                    "count": len(MODEL_REGISTRY)
                }))
                continue
            
            # Handle regular chat requests
            request_id = message.get("request_id")
            data = message.get("data")
            logging.debug(f"⬅️ BROWSER [ID: {request_id}]: Received data: {data}")

            if request_id in response_channels:
                queue = response_channels[request_id]
                logging.debug(f"BROWSER [ID: {request_id}]: Queue size before put: {queue.qsize()}")
                await queue.put(data)
                logging.debug(f"BROWSER [ID: {request_id}]: Put data on queue. New size: {queue.qsize()}")
            else:
                logging.warning(f"⚠️ BROWSER: Msg for unknown/closed request_id: {request_id}")
    except WebSocketDisconnect:
        logging.warning("❌ Browser client disconnected.")
    finally:
        browser_ws = None
        for queue in response_channels.values():
            await queue.put({"error": "Browser disconnected"})
        response_channels.clear()
        logging.info("WebSocket and channels cleaned up.")

# --- API Handler ---
@app.post("/v1/chat/completions")
async def chat_completions(request: Request):
    if not browser_ws:
        raise HTTPException(status_code=503, detail="Browser client not connected.")
    
    openai_req = await request.json()
    request_id = str(uuid.uuid4())
    is_streaming = openai_req.get("stream", True)
    model_name = openai_req.get("model")

    model_info = MODEL_REGISTRY.get(model_name)
    if not model_info:
        raise HTTPException(status_code=404, detail=f"Model '{model_name}' not found.")
    model_type = model_info.get("type", "chat")
    
    response_channels[request_id] = asyncio.Queue(maxsize=BACKPRESSURE_QUEUE_SIZE)
    logging.info(f"API [ID: {request_id}]: Created queue for model type '{model_type}'.")

    try:
        asyncio.create_task(send_to_browser_task(request_id, openai_req))
        
        media_type = "text/event-stream" if is_streaming else "application/json"
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        } if is_streaming else {}

        logging.info(f"API [ID: {request_id}]: Returning {media_type} response to client.")
        return StreamingResponse(
            stream_generator(request_id, model_name, is_streaming=is_streaming, model_type=model_type),
            media_type=media_type,
            headers=headers
        )
    except Exception as e:
        if request_id in response_channels:
            del response_channels[request_id]
        logging.error(f"API [ID: {request_id}]: Exception: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

async def send_to_browser_task(request_id: str, openai_req: dict):
    """This task runs in the background, sending the request to the browser."""
    if not browser_ws:
        logging.error(f"TASK [ID: {request_id}]: Cannot send, browser disconnected.")
        return

    try:
        lmarena_payload, files_to_upload = create_lmarena_request_body(openai_req)
        
        message_to_browser = {
            "request_id": request_id,
            "payload": lmarena_payload,
            "files_to_upload": files_to_upload
        }
        
        logging.info(f"TASK [ID: {request_id}]: Sending payload and {len(files_to_upload)} file(s) to browser.")
        await browser_ws.send_text(json.dumps(message_to_browser))
        logging.info(f"TASK [ID: {request_id}]: Payload sent.")

    except Exception as e:
        logging.error(f"Error creating or sending request body: {e}", exc_info=True)
        if request_id in response_channels:
            await response_channels[request_id].put({"error": f"Failed to process request: {e}"})

# --- Stream Consumer ---
async def stream_generator(request_id: str, model: str, is_streaming: bool, model_type: str):
    queue = response_channels.get(request_id)
    if not queue:
        logging.error(f"STREAMER [ID: {request_id}]: Queue not found!")
        return

    logging.info(f"STREAMER [ID: {request_id}]: Generator started for model type '{model_type}'.")
    await asyncio.sleep(0)

    response_id = f"chatcmpl-{uuid.uuid4()}"
    
    try:
        accumulated_content = ""
        media_urls = []
        finish_reason = None

        while True:
            raw_data = await queue.get()
            if (isinstance(raw_data, dict) and 'error' in raw_data) or raw_data == "[DONE]":
                if isinstance(raw_data, dict):
                    logging.error(f"STREAMER [ID: {request_id}]: Received error from browser: {raw_data['error']}")
                break

            try:
                prefix, content = raw_data.split(":", 1)
                
                if model_type in ["image", "video"] and prefix == "a2":
                    media_data_list = json.loads(content)
                    for item in media_data_list:
                        url = item.get("image") if model_type == "image" else item.get("url")
                        if url:
                            logging.info(f"MEDIA [ID: {request_id}]: Found {model_type} URL: {url}")
                            media_urls.append(url)
                
                elif model_type == "chat" and prefix == "a0":
                    delta = json.loads(content)
                    if is_streaming:
                        chunk = {
                            "id": response_id, "object": "chat.completion.chunk",
                            "created": int(asyncio.get_event_loop().time()), "model": model,
                            "choices": [{"index": 0, "delta": {"role": "assistant", "content": delta}, "finish_reason": None}]
                        }
                        yield f"data: {json.dumps(chunk)}\n\n"
                    else:
                        accumulated_content += delta
                
                elif prefix == "ad":
                    finish_data = json.loads(content)
                    finish_reason = finish_data.get("finishReason", "stop")

            except (ValueError, json.JSONDecodeError):
                logging.warning(f"STREAMER [ID: {request_id}]: Could not parse data: {raw_data}")
                continue
        
        # --- Final Response Generation ---
        if model_type in ["image", "video"]:
            logging.info(f"MEDIA [ID: {request_id}]: Found {len(media_urls)} media file(s). Returning URLs directly.")
            # Format the URLs based on their type
            if model_type == "video":
                accumulated_content = "\n".join(media_urls) # Return raw URLs for videos
            else: # Default to image handling
                accumulated_content = "\n".join([f"![Generated Image]({url})" for url in media_urls])

        if is_streaming:
            if model_type in ["image", "video"]:
                chunk = {
                    "id": response_id, "object": "chat.completion.chunk",
                    "created": int(asyncio.get_event_loop().time()), "model": model,
                    "choices": [{"index": 0, "delta": {"role": "assistant", "content": accumulated_content}, "finish_reason": finish_reason or "stop"}]
                }
                yield f"data: {json.dumps(chunk)}\n\n"
            yield "data: [DONE]\n\n"
        else:
            # For non-streaming, send the complete JSON object with the URL content
            complete_response = {
                "id": response_id, "object": "chat.completion",
                "created": int(asyncio.get_event_loop().time()), "model": model,
                "choices": [{"index": 0, "message": {"role": "assistant", "content": accumulated_content}, "finish_reason": finish_reason or "stop"}],
                "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
            }
            yield json.dumps(complete_response)

    except asyncio.CancelledError:
        logging.warning(f"GENERATOR [ID: {request_id}]: Client disconnected.")
    except Exception as e:
        logging.error(f"GENERATOR [ID: {request_id}]: Error: {e}", exc_info=True)
    finally:
        if request_id in response_channels:
            del response_channels[request_id]
            logging.info(f"GENERATOR [ID: {request_id}]: Cleaned up response channel.")

def create_lmarena_request_body(openai_req: dict) -> (dict, list):
    model_name = openai_req["model"]
    
    if model_name not in MODEL_REGISTRY:
        raise ValueError(f"Model '{model_name}' not found in registry. Available models: {list(MODEL_REGISTRY.keys())}")
    
    model_info = MODEL_REGISTRY[model_name]
    model_id = model_info.get("id", model_name)
    modality = model_info.get("type", "chat")
    evaluation_id = str(uuid.uuid4())
    
    files_to_upload = []
    processed_messages = []

    # Process messages to extract files and clean content
    for msg in openai_req['messages']:
        content = msg.get("content", "")
        new_msg = msg.copy()
        
        if isinstance(content, list):
            # Handle official multimodal content array
            text_parts = []
            for part in content:
                if part.get("type") == "text":
                    text_parts.append(part.get("text", ""))
                elif part.get("type") == "image_url":
                    image_url = part.get("image_url", {}).get("url", "")
                    match = re.match(r"data:(image/\w+);base64,(.*)", image_url)
                    if match:
                        mime_type, base64_data = match.groups()
                        file_ext = mime_type.split('/')
                        filename = f"upload-{uuid.uuid4()}.{file_ext}"
                        files_to_upload.append({"fileName": filename, "contentType": mime_type, "data": base64_data})
            new_msg["content"] = "\n".join(text_parts)
            processed_messages.append(new_msg)
        
        elif isinstance(content, str):
            # Handle simple string content that might contain data URLs
            text_content = content
            matches = re.findall(r"data:(image/\w+);base64,([a-zA-Z0-9+/=]+)", content)
            if matches:
                logging.info(f"Found {len(matches)} data URL(s) in string content.")
                for mime_type, base64_data in matches:
                    file_ext = mime_type.split('/')
                    filename = f"upload-{uuid.uuid4()}.{file_ext}"
                    files_to_upload.append({"fileName": filename, "contentType": mime_type, "data": base64_data})
                # Remove all found data URLs from the text to be sent
                text_content = re.sub(r"data:image/\w+;base64,[a-zA-Z0-9+/=]+", "", text_content).strip()
            
            new_msg["content"] = text_content
            processed_messages.append(new_msg)
        
        else:
            # If content is not a list or string, just pass it through
            processed_messages.append(msg)

    # Build Arena-formatted messages
    arena_messages = []
    message_ids = [str(uuid.uuid4()) for _ in processed_messages]
    for i, msg in enumerate(processed_messages):
        parent_message_ids = [message_ids[i-1]] if i > 0 else []
        
        original_role = msg.get("role")
        role = "user" if original_role not in ["user", "assistant", "data"] else original_role
            
        arena_messages.append({
            "id": message_ids[i], "role": role, "content": msg['content'],
            "experimental_attachments": [], "parentMessageIds": parent_message_ids,
            "participantPosition": "a", "modelId": model_id if role == 'assistant' else None,
            "evaluationSessionId": evaluation_id, "status": "pending", "failureReason": None,
        })

    # Find the last user message to use as parent for the hidden space message
    last_user_message_index = -1
    for i in range(len(processed_messages) - 1, -1, -1):
        if processed_messages[i].get("role") == "user":
            last_user_message_index = i
            break

    # Add a hidden user message with space content after the last user message
    hidden_user_message_id = str(uuid.uuid4())
    if last_user_message_index >= 0 and last_user_message_index < len(message_ids):
        last_user_message_id = message_ids[last_user_message_index]
    else:
        # Fallback: use the last message ID if no user message found
        last_user_message_id = message_ids[-1] if message_ids else str(uuid.uuid4())

    arena_messages.append({
        "id": hidden_user_message_id, "role": "user", "content": " ",
        "experimental_attachments": [], "parentMessageIds": [last_user_message_id],
        "participantPosition": "a", "modelId": None,
        "evaluationSessionId": evaluation_id, "status": "pending", "failureReason": None,
    })

    user_message_id = hidden_user_message_id  # Use the hidden message as the last user message
    model_a_message_id = str(uuid.uuid4())
    arena_messages.append({
        "id": model_a_message_id, "role": "assistant", "content": "",
        "experimental_attachments": [], "parentMessageIds": [user_message_id],
        "participantPosition": "a", "modelId": model_id,
        "evaluationSessionId": evaluation_id, "status": "pending", "failureReason": None,
    })

    payload = {
        "id": evaluation_id, "mode": "direct", "modelAId": model_id,
        "userMessageId": user_message_id, "modelAMessageId": model_a_message_id,
        "messages": arena_messages, "modality": modality,
    }
    
    return payload, files_to_upload

@app.get("/v1/models")
async def get_models():
    """Lists all available models in an OpenAI-compatible format."""
    return {
        "object": "list",
        "data": [
            {
                "id": model_name,
                "object": "model",
                "created": int(asyncio.get_event_loop().time()),
                "owned_by": "lmarena",
                "type": model_info.get("type", "chat")
            }
            for model_name, model_info in MODEL_REGISTRY.items()
        ],
    }

@app.post("/v1/refresh-models")
async def refresh_models():
    """Request model registry refresh from browser script."""
    if browser_ws:
        try:
            # Send refresh request to browser
            await browser_ws.send_text(json.dumps({
                "type": "refresh_models"
            }))
            
            return {
                "success": True,
                "message": "Model refresh request sent to browser",
                "models": list(MODEL_REGISTRY.keys())
            }
        except Exception as e:
            logging.error(f"Failed to send refresh request: {e}")
            return {
                "success": False,
                "message": "Failed to send refresh request to browser",
                "models": list(MODEL_REGISTRY.keys())
            }
    else:
        return {
            "success": False,
            "message": "No browser connection available",
            "models": list(MODEL_REGISTRY.keys())
        }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=4452)
