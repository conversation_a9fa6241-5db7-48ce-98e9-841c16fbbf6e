import asyncio
import json
import logging
import uuid
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException
from fastapi.responses import StreamingResponse

import time

# --- Basic Configuration ---
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Model Registry ---
MODEL_REGISTRY = {
    "wolfstride": {"id": "849f6833-8598-4329-b7a6-e765eb2a0c0d", "type": "chat"},
    "claude-3-5-sonnet-20241022": {"id": "f44e280a-7914-43ca-a25d-ecfcc5d48d09", "type": "chat"},
    "gemini-2.0-flash-001": {"id": "7a55108b-b997-4cff-a72f-5aa83beee918", "type": "chat"},
    "chatgpt-4o-latest-20250326": {"id": "9513524d-882e-4350-b31e-e4584440c2c8", "type": "chat"},
    "llama-4-maverick-03-26-experimental": {"id": "49bd7403-c7fd-4d91-9829-90a91906ad6c", "type": "chat"},
    "gpt-4.1-2025-04-14": {"id": "14e9311c-94d2-40c2-8c54-273947e208b0", "type": "chat"},
    "qwq-32b": {"id": "885976d3-d178-48f5-a3f4-6e13e0718872", "type": "chat"},
    "grok-3-preview-02-24": {"id": "bd2c8278-af7a-4ec3-84db-0a426c785564", "type": "chat"},
    "claude-3-7-sonnet-20250219-thinking-32k": {"id": "be98fcfd-345c-4ae1-9a82-a19123ebf1d2", "type": "chat"},
    "gpt-4.1-mini-2025-04-14": {"id": "6a5437a7-c786-467b-b701-17b0bc8c8231", "type": "chat"},
    "grok-3-mini-beta": {"id": "7699c8d4-0742-42f9-a117-d10e84688dab", "type": "chat"},
    "o3-2025-04-16": {"id": "cb0f1e24-e8e9-4745-aabc-b926ffde7475", "type": "chat"},
    "claude-3-7-sonnet-20250219": {"id": "c5a11495-081a-4dc6-8d9a-64a4fd6f7bbc", "type": "chat"},
    "claude-opus-4-20250514": {"id": "ee116d12-64d6-48a8-88e5-b2d06325cdd2", "type": "chat"},
    "claude-sonnet-4-20250514": {"id": "ac44dd10-0666-451c-b824-386ccfea7bcc", "type": "chat"},
    "claude-3-5-haiku-20241022": {"id": "f6fbf06c-532c-4c8a-89c7-f3ddcfb34bd1", "type": "chat"},
    "o4-mini-2025-04-16": {"id": "f1102bbf-34ca-468f-a9fc-14bcf63f315b", "type": "chat"},
    "o3-mini": {"id": "c680645e-efac-4a81-b0af-da16902b2541", "type": "chat"},
    "gemma-3-27b-it": {"id": "789e245f-eafe-4c72-b563-d135e93988fc", "type": "chat"},
    "gemini-2.5-flash-preview-04-17": {"id": "7fff29a7-93cc-44ab-b685-482c55ce4fa6", "type": "chat"},
    "amazon.nova-pro-v1:0": {"id": "a14546b5-d78d-4cf6-bb61-ab5b8510a9d6", "type": "chat"},
    "command-a-03-2025": {"id": "0f785ba1-efcb-472d-961e-69f7b251c7e3", "type": "chat"},
    "mistral-medium-2505": {"id": "27b9f8c6-3ee1-464a-9479-a8b3c2a48fd4", "type": "chat"},
    "deepseek-v3-0324": {"id": "2f5253e4-75be-473c-bcfc-baeb3df0f8ad", "type": "chat"},
    "qwen3-235b-a22b": {"id": "2595a594-fa54-4299-97cd-2d7380d21c80", "type": "chat"},
    "qwen-max-2025-01-25": {"id": "fe8003fc-2e5d-4a3f-8f07-c1cff7ba0159", "type": "chat"},
    "llama-3.3-70b-instruct": {"id": "dcbd7897-5a37-4a34-93f1-76a24c7bb028", "type": "chat"},
    "qwen3-30b-a3b": {"id": "9a066f6a-7205-4325-8d0b-d81cc4b049c0", "type": "chat"},
    "llama-4-maverick-17b-128e-instruct": {"id": "b5ad3ab7-fc56-4ecd-8921-bd56b55c1159", "type": "chat"},
    "gemini-2.5-pro-preview-05-06": {"id": "0337ee08-8305-40c0-b820-123ad42b60cf", "type": "chat"},
    "gemini-2.0-flash-preview-image-generation": {"id": "69bbf7d4-9f44-447e-a868-abc4f7a31810", "type": "image"},
    "imagen-3.0-generate-002": {"id": "51ad1d79-61e2-414c-99e3-faeb64bb6b1b", "type": "image"},
    "ideogram-v2": {"id": "34ee5a83-8d85-4d8b-b2c1-3b3413e9ed98", "type": "image"},
    "gpt-image-1": {"id": "6e855f13-55d7-4127-8656-9168a9f4dcc0", "type": "image"},
    "photon": {"id": "17e31227-36d7-4a7a-943a-7ebffa3a00eb", "type": "image"},
    "dall-e-3": {"id": "bb97bc68-131c-4ea4-a59e-03a6252de0d2", "type": "image"},
    "recraft-v3": {"id": "b70ab012-18e7-4d6f-a887-574e05de6c20", "type": "image"},
    "flux-1.1-pro": {"id": "9e8525b7-fe50-4e50-bf7f-ad1d3d205d3c", "type": "image"},
}

# --- NEW ARCHITECTURE: Global state for managing response channels ---
browser_ws: WebSocket | None = None
# This dictionary will map a request_id to the asyncio.Queue for its response stream.
response_channels: dict[str, asyncio.Queue] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    logging.info("Server startup complete. Waiting for browser to connect...")
    yield
    logging.info("Server shutting down.")

app = FastAPI(lifespan=lifespan)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    Handles the WebSocket connection from the browser and acts as a router,
    dispatching incoming messages to the correct response channel.
    """
    global browser_ws
    await websocket.accept()
    logging.info("✅ WebSocket connection from browser accepted.")
    browser_ws = websocket
    try:
        while True:
            message_str = await websocket.receive_text()
            message = json.loads(message_str)
            request_id = message.get("request_id")
            data = message.get("data")
            logging.debug(f"⬅️ BROWSER MSG [ID: {request_id}]: Received raw data: {data}")

            # Find the correct response channel and put the data in it
            if request_id in response_channels:
                await response_channels[request_id].put(data)
                logging.debug(f"➡️ BROWSER MSG [ID: {request_id}]: Put data into its response channel.")
            else:
                logging.warning(f"⚠️ BROWSER MSG: Received message for unknown/closed request_id: {request_id}")
    except WebSocketDisconnect:
        logging.warning("❌ Browser client disconnected.")
    finally:
        browser_ws = None
        # Signal an error to any streams that were still waiting for the browser
        for queue in response_channels.values():
            await queue.put({"error": "Browser disconnected during operation"})
        response_channels.clear()
        logging.info("WebSocket cleanup complete.")

@app.post("/v1/chat/completions")
async def chat_completions(request: Request):
    """
    Sets up a response channel for a new request, tells the browser to start,
    and then streams the data received on that channel back to the client.
    """
    logging.info("--- NEW REQUEST ---")
    if not browser_ws:
        raise HTTPException(status_code=503, detail="Browser client not connected.")

    try:
        openai_req = await request.json()
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON body")

    model_name = openai_req.get("model")
    if not model_name or model_name not in MODEL_REGISTRY:
        raise HTTPException(status_code=400, detail=f"Model not found: {model_name}")

    request_id = str(uuid.uuid4())
    response_channels[request_id] = asyncio.Queue()
    logging.info(f"API CALL [ID: {request_id}]: Created response channel.")

    try:
        lmarena_payload = create_lmarena_request_body(openai_req)
        message_to_browser = {"request_id": request_id, "payload": lmarena_payload}
        
        logging.info(f"API CALL [ID: {request_id}]: Sending payload to browser.")
        await browser_ws.send_text(json.dumps(message_to_browser))

        return StreamingResponse(
            stream_generator(request_id, model_name),
            media_type="text/event-stream"
        )
    except Exception as e:
        # Clean up the channel if setup fails
        if request_id in response_channels:
            del response_channels[request_id]
        logging.error(f"API CALL [ID: {request_id}]: Unhandled exception during setup: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/v1/models")
async def get_models():
    return {
        "object": "list",
        "data": [
            {
                "id": model_name, "object": "model",
                "created": int(asyncio.get_event_loop().time()), "owned_by": "lmsys"
            }
            for model_name in MODEL_REGISTRY.keys()
        ],
    }

def create_lmarena_request_body(openai_req: dict) -> dict:
    model_name = openai_req["model"]
    model_info = MODEL_REGISTRY[model_name]
    model_id = model_info["id"]
    modality = model_info["type"]
    evaluation_id = str(uuid.uuid4())
    arena_messages = []
    message_ids = [str(uuid.uuid4()) for _ in openai_req['messages']]
    for i, msg in enumerate(openai_req['messages']):
        parent_message_ids = [message_ids[i-1]] if i > 0 else []
        role = "user" if msg['role'] == "system" else msg['role']
        arena_messages.append({
            "id": message_ids[i], "role": role, "content": msg['content'],
            "experimental_attachments": [], "parentMessageIds": parent_message_ids,
            "participantPosition": "a", "modelId": model_id if msg['role'] == 'assistant' else None,
            "evaluationSessionId": evaluation_id, "status": "pending", "failureReason": None,
        })
    user_message_id = message_ids[-1] if message_ids else str(uuid.uuid4())
    model_a_message_id = str(uuid.uuid4())
    arena_messages.append({
        "id": model_a_message_id, "role": "assistant", "content": "",
        "experimental_attachments": [], "parentMessageIds": [user_message_id],
        "participantPosition": "a", "modelId": model_id,
        "evaluationSessionId": evaluation_id, "status": "pending", "failureReason": None,
    })
    return {
        "id": evaluation_id, "mode": "direct", "modelAId": model_id,
        "userMessageId": user_message_id, "modelAMessageId": model_a_message_id,
        "messages": arena_messages, "modality": modality,
    }

# --- CORRECTED STREAMING LOGIC ---
async def stream_generator(request_id: str, model: str):
    """
    This generator is now a pure consumer. It waits for data to be put on its
    channel by the websocket_endpoint and yields it immediately.
    """
    logging.info(f"STREAMER [ID: {request_id}]: Generator started, waiting for data.")
    queue = response_channels[request_id]
    response_id = f"chatcmpl-{uuid.uuid4()}"
    
    try:
        while True:
            # This will pause the generator until the websocket handler puts something in the queue.
            raw_data = await queue.get()

            # Check for termination signals
            if (isinstance(raw_data, dict) and 'error' in raw_data) or raw_data == "[DONE]":
                await asyncio.sleep(1)
                if raw_data != "[DONE]":
                    logging.error(f"STREAMER [ID: {request_id}]: Received error signal: {raw_data['error']}")
                else:
                    logging.info(f"STREAMER [ID: {request_id}]: Received [DONE] signal.")
                break

            # Process and yield the data chunk immediately
            try:
                prefix, content = raw_data.split(":", 1)
                if prefix == "a0":
                    delta_content = json.loads(content)
                    chunk = {
                        "id": response_id, "object": "chat.completion.chunk",
                        "created": int(time.time()), "model": model,
                        "choices": [{"index": 0, "delta": {"role": "assistant", "content": delta_content}}]
                    }
                    sse_chunk = f"data: {json.dumps(chunk)}\n\n"
                    yield sse_chunk
                    await asyncio.sleep(0.01)
            except (ValueError, json.JSONDecodeError):
                logging.warning(f"STREAMER [ID: {request_id}]: Could not parse stream data chunk: '{raw_data}'")
        
        # Send the final SSE message
        yield "data: [DONE]\n\n"
        logging.info(f"STREAMER [ID: {request_id}]: Final [DONE] message sent to client.")

    finally:
        # Crucial cleanup to prevent memory leaks
        if request_id in response_channels:
            del response_channels[request_id]
            logging.info(f"STREAMER [ID: {request_id}]: Generator finished, response channel cleaned up.")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=4452)