// ==UserScript==
// @name         LM Arena Websocket
// @namespace    http://tampermonkey.net/
// @version      2025-07-07
// @description  try to take over the world!
// <AUTHOR>
// @match        https://canary.lmarena.ai/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=discord.com
// @grant        none
// ==/UserScript==
(function () {
    'use strict';

    // --- Configuration ---
    const SERVER_URL = "ws://localhost:4452/ws";
    const TARGET_API_PATH = "/api/stream/create-evaluation";
    let socket;
    let isRefreshing = false;
    let pendingRequests = [];
    let modelRegistrySent = false;

    async function waitForCloudflareAuth() {
        console.log("[Injector] ⏳ Waiting for Cloudflare authentication to complete...");
        
        const maxWaitTime = 30000; // 30 seconds max wait
        const checkInterval = 7000; // Check every 1 second
        let waitTime = 0;
        
        while (waitTime < maxWaitTime) {
            try {
                // Test if CF auth is complete by making a simple request
                const testResponse = await fetch('https://canary.lmarena.ai/', {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    }
                });
                
                const responseText = await testResponse.text();
                
                // If we get the normal page (not a CF challenge), auth is complete
                if (!isCloudflareChallenge(responseText) && testResponse.ok) {
                    console.log(`[Injector] ✅ Cloudflare authentication completed after ${waitTime}ms`);
                    return true;
                }
                
                console.log(`[Injector] ⏳ Still waiting for CF auth... (${waitTime}ms elapsed)`);
                
            } catch (error) {
                console.log(`[Injector] ⏳ CF auth check failed, continuing to wait... (${waitTime}ms elapsed)`);
            }
            
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitTime += checkInterval;
        }
        
        console.warn(`[Injector] ⚠️ CF authentication wait timeout after ${maxWaitTime}ms`);
        return false;
    }

    async function processPendingRequests() {
        // Retrieve pending requests from localStorage (survives page refresh)
        const storedRequests = localStorage.getItem('lmarena_pending_requests');
        if (storedRequests) {
            try {
                const requests = JSON.parse(storedRequests);
                console.log(`[Injector] 🔄 Found ${requests.length} pending requests after CF refresh`);
                
                // Wait for CF authentication to complete before processing requests
                const authComplete = await waitForCloudflareAuth();
                
                if (!authComplete) {
                    console.error("[Injector] ❌ CF authentication timeout, will retry requests anyway");
                }
                
                // Clear the stored requests
                localStorage.removeItem('lmarena_pending_requests');
                
                // Process each pending request
                for (const { requestId, payload } of requests) {
                    console.log(`[Injector] 🔄 Retrying request ${requestId} after CF refresh`);
                    
                    // Add a small delay between requests to avoid overwhelming the server
                    if (requests.indexOf({ requestId, payload }) > 0) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                    
                    await executeFetchAndStreamBack(requestId, payload);
                }
            } catch (error) {
                console.error("[Injector] ❌ Error processing pending requests:", error);
                localStorage.removeItem('lmarena_pending_requests');
            }
        }
    }

    function connect() {
        console.log(`[Injector] Connecting to server at ${SERVER_URL}...`);
        socket = new WebSocket(SERVER_URL);

        socket.onopen = () => {
            console.log("[Injector] ✅ Connection established with local server.");
            
            // Check if we have pending requests from before a CF refresh
            processPendingRequests();
            
            // Send model registry after connection
            if (!modelRegistrySent) {
                setTimeout(() => {
                    sendModelRegistry();
                }, 2000); // Wait a bit for page to fully load
            }
        };

        socket.onmessage = async (event) => {
            try {
                const message = JSON.parse(event.data);
                
                if (message.type === 'refresh_models') {
                    console.log('[Injector] 🔄 Received model refresh request');
                    sendModelRegistry();
                    return;
                }
                
                if (message.type === 'model_registry_ack') {
                    console.log(`[Injector] ✅ Model registry updated with ${message.count} models`);
                    modelRegistrySent = true;
                    return;
                }
                
                const { request_id, payload, files_to_upload } = message;

                if (!request_id || !payload) {
                    console.error("[Injector] Invalid message from server:", message);
                    return;
                }

                // Check if there are files to upload and route to the correct handler
                if (files_to_upload && files_to_upload.length > 0) {
                    console.log(`[Injector] ⬆️ Received request with ${files_to_upload.length} file(s). Starting upload process.`);
                    await handleUploadAndChat(request_id, payload, files_to_upload);
                } else {
                    console.log(`[Injector] ⬇️ Received standard text request ${request_id}. Firing fetch.`);
                    await executeFetchAndStreamBack(request_id, payload);
                }

            } catch (error) {
                console.error("[Injector] Error processing message from server:", error);
            }
        };

        socket.onclose = () => {
            console.warn("[Injector] 🔌 Connection to local server closed. Retrying in 5 seconds...");
            modelRegistrySent = false; // Reset flag on disconnect
            setTimeout(connect, 5000);
        };

        socket.onerror = (error) => {
            console.error("[Injector] ❌ WebSocket error:", error);
            socket.close(); // This will trigger the onclose reconnect logic
        };
    }

    function isCloudflareChallenge(responseText) {
        // Check for common Cloudflare challenge indicators
        return responseText.includes('Checking your browser before accessing') ||
               responseText.includes('DDoS protection by Cloudflare') ||
               responseText.includes('cf-browser-verification') ||
               responseText.includes('cf-challenge-running') ||
               responseText.includes('__cf_chl_jschl_tk__') ||
               responseText.includes('cloudflare-static') ||
               responseText.includes('<title>Just a moment...</title>') ||
               responseText.includes('Enable JavaScript and cookies to continue') ||
               responseText.includes('window._cf_chl_opt') ||
               (responseText.includes('cloudflare') && responseText.includes('challenge'));
    }

    async function handleCloudflareRefresh() {
        if (isRefreshing) {
            console.log("[Injector] 🔄 Already refreshing, skipping duplicate refresh request");
            return;
        }

        isRefreshing = true;
        console.log("[Injector] 🔄 Cloudflare challenge detected! Refreshing page to get new token...");
        
        try {
            // Check if we have any pending requests stored
            const storedRequests = localStorage.getItem('lmarena_pending_requests');
            if (storedRequests) {
                const requests = JSON.parse(storedRequests);
                console.log(`[Injector] 💾 Found ${requests.length} pending requests, refreshing page...`);
            }
            
            // Refresh the page to trigger new CF authentication
            window.location.reload();
            
            // Wait for page to reload and CF auth to complete
            // The script will restart after reload, so this won't continue
        } catch (error) {
            console.error("[Injector] ❌ Error during CF refresh:", error);
            isRefreshing = false;
        }
    }

    // Helper function to convert base64 to a Blob
    function base64ToBlob(base64, contentType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: contentType });
    }

    async function handleUploadAndChat(requestId, payload, filesToUpload) {
        try {
            const attachments = [];
            for (const file of filesToUpload) {
                console.log(`[Uploader] Processing file: ${file.fileName}`);

                // Step 1: Get Signed URL
                console.log(`[Uploader] Step 1: Getting signed URL for ${file.fileName}`);
                const signUrlResponse = await fetch('https://canary.lmarena.ai/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'text/plain;charset=UTF-8',
                        'Accept': 'text/x-component',
                        'next-action': '70371ce4df6690b096dcb846bea1f7597a6fc2b802',
                        'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%5B%22locale%22%2C%22en%22%2C%22d%22%5D%2C%7B%22children%22%3A%5B%22(app)%22%2C%7B%22children%22%3A%5B%22(with-sidebar)%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2F%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D%7D%5D%7D%5D',
                        'origin': 'https://canary.lmarena.ai',
                        'referer': 'https://canary.lmarena.ai/'
                    },
                    body: JSON.stringify([file.fileName, file.contentType])
                });
                const signUrlText = await signUrlResponse.text();
                console.log("[Uploader] Received for signed URL:", signUrlText);

                // The response is prefixed (e.g., "0:{...}" or "1:{...}"). Find the JSON part.
                // The response contains multiple JSON objects. We need the one prefixed with "1:".
                const match = signUrlText.match(/1:({.*})/);
                if (!match || match.length < 2) {
                    throw new Error('Could not find the signed URL data block (prefixed with "1:") in the response.');
                }
                
                // The regex match returns an array; the captured JSON is the second element.
                const jsonString = match[1];
                const signUrlData = JSON.parse(jsonString);

                if (!signUrlData || !signUrlData.data || !signUrlData.data.uploadUrl) {
                    throw new Error('Signed URL data is incomplete or invalid after parsing.');
                }
                const { uploadUrl, key } = signUrlData.data;
                console.log(`[Uploader] Got signed URL. Key: ${key}`);

                // Step 2: Upload file to storage
                console.log(`[Uploader] Step 2: Uploading file to cloud storage...`);
                const blob = base64ToBlob(file.data, file.contentType);
                const uploadResponse = await fetch(uploadUrl, {
                    method: 'PUT',
                    headers: { 'Content-Type': file.contentType },
                    body: blob
                });
                if (!uploadResponse.ok) throw new Error(`File upload failed with status ${uploadResponse.status}`);
                console.log(`[Uploader] File uploaded successfully.`);

                // Step 3: Notify LMArena of upload
                console.log(`[Uploader] Step 3: Notifying LMArena of upload completion...`);
                const notifyResponse = await fetch('https://canary.lmarena.ai/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'text/plain;charset=UTF-8',
                        'Accept': 'text/x-component',
                        'next-action': '6035350836ff053e53aad3c56acd8b0a3161ac8243',
                        'next-router-state-tree': '%5B%22%22%2C%7B%22children%22%3A%5B%5B%22locale%22%2C%22en%22%2C%22d%22%5D%2C%7B%22children%22%3A%5B%22(app)%22%2C%7B%22children%22%3A%5B%22(with-sidebar)%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2F%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D%7D%5D%7D%5D',
                        'origin': 'https://canary.lmarena.ai',
                        'referer': 'https://canary.lmarena.ai/'
                    },
                    body: JSON.stringify([key])
                });
                const notifyText = await notifyResponse.text();
                console.log(`[Uploader] Notification sent. Response:`, notifyText);

                // Parse the response from the notification step to get the final URL
                const finalUrlDataLine = notifyText.split('\n').find(line => line.startsWith('1:'));
                if (!finalUrlDataLine) throw new Error('Could not find final URL data in notification response.');

                const finalUrlData = JSON.parse(finalUrlDataLine.substring(2));
                const finalUrl = finalUrlData.data.url;
                if (!finalUrl) throw new Error('Final URL not found in notification response data.');

                console.log(`[Uploader] Extracted final GetObject URL: ${finalUrl}`);

                attachments.push({
                    name: key,
                    contentType: file.contentType,
                    url: finalUrl
                });
            }

            // Step 4: Modify payload with attachments and send final request
            console.log('[Uploader] All files uploaded. Modifying final payload...');
            const userMessage = payload.messages.find(m => m.role === 'user');
            if (userMessage) {
                userMessage.experimental_attachments = attachments;
            } else {
                throw new Error("Could not find user message in payload to attach files to.");
            }
            
            console.log('[Uploader] Payload modified. Initiating final chat stream.');
            await executeFetchAndStreamBack(requestId, payload);

        } catch (error) {
            console.error(`[Uploader] Error during file upload process for request ${requestId}:`, error);
            sendToServer(requestId, JSON.stringify({ error: `File upload failed: ${error.message}` }));
            sendToServer(requestId, "[DONE]");
        }
    }

    async function executeFetchAndStreamBack(requestId, payload) {
        try {
            const response = await fetch(`https://canary.lmarena.ai${TARGET_API_PATH}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/plain;charset=UTF-8',
                    'Accept': '*/*',
                    // The browser automatically adds critical headers:
                    // Cookie, User-Agent, sec-ch-ua, etc.
                },
                body: JSON.stringify(payload)
            });

            // Check if we got a Cloudflare challenge instead of the expected response
            if (!response.ok || response.headers.get('content-type')?.includes('text/html')) {
                const responseText = await response.text();
                
                if (isCloudflareChallenge(responseText)) {
                    console.log(`[Injector] 🛡️ Cloudflare challenge detected for request ${requestId} (Status: ${response.status})`);
                    
                    // Store the request for retry after refresh
                    const existingRequests = JSON.parse(localStorage.getItem('lmarena_pending_requests') || '[]');
                    existingRequests.push({ requestId, payload });
                    localStorage.setItem('lmarena_pending_requests', JSON.stringify(existingRequests));
                    
                    // Trigger automatic refresh (don't await to prevent blocking)
                    handleCloudflareRefresh();
                    return; // Function will not continue after page refresh
                }
                
                // If it's not a CF challenge, treat as regular error
                throw new Error(`Fetch failed with status ${response.status}: ${responseText}`);
            }

            if (!response.body) {
                throw new Error(`No response body received for request ${requestId}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    console.log(`[Injector] ✅ Stream finished for request ${requestId}.`);
                    sendToServer(requestId, "[DONE]");
                    break;
                }

                const chunk = decoder.decode(value);
                
                // Additional check: if we get HTML in the stream, it might be a CF challenge
                if (chunk.includes('<html') || chunk.includes('<!DOCTYPE')) {
                    if (isCloudflareChallenge(chunk)) {
                        console.log(`[Injector] 🛡️ Cloudflare challenge detected in stream for request ${requestId}`);
                        
                        // Store the request for retry after refresh
                        const existingRequests = JSON.parse(localStorage.getItem('lmarena_pending_requests') || '[]');
                        existingRequests.push({ requestId, payload });
                        localStorage.setItem('lmarena_pending_requests', JSON.stringify(existingRequests));
                        
                        // Trigger automatic refresh (don't await to prevent blocking)
                        handleCloudflareRefresh();
                        return;
                    }
                }
                
                // The stream often sends multiple lines in one chunk
                const lines = chunk.split('\n').filter(line => line.trim() !== '');
                for (const line of lines) {
                    sendToServer(requestId, line);
                }
            }

        } catch (error) {
            console.error(`[Injector] ❌ Error during fetch for request ${requestId}:`, error);
            sendToServer(requestId, JSON.stringify({ error: error.message }));
            sendToServer(requestId, "[DONE]"); // Ensure the stream is always terminated
        }
    }

    function sendToServer(requestId, data) {
        if (socket && socket.readyState === WebSocket.OPEN) {
            const message = {
                request_id: requestId,
                data: data
            };
            socket.send(JSON.stringify(message));
        } else {
            console.error("[Injector] Cannot send data, socket is not open.");
        }
    }

    function extractModelRegistry() {
        console.log('[Injector] 🔍 Extracting model registry from script tags...');
        
        try {
            const scripts = document.querySelectorAll('script');
            let modelData = null;
            const searchString = 'self.__next_f.push([1,"4:';

            for (const script of scripts) {
                const content = script.textContent || script.innerHTML;
                if (content.includes(searchString)) {
                    console.log('[Injector] Found the target script tag.');

                    // The entire payload is inside this one script's content.
                    const startIndex = content.indexOf(searchString);
                    const payloadStartIndex = startIndex + searchString.length; // Start at the first '[' after the ':'
                    
                    let balance = 1;
                    let endIndex = -1;
                    let inString = false;

                    // Start searching from after the first bracket
                    for (let i = payloadStartIndex + 1; i < content.length; i++) {
                        const char = content[i];
                        const prevChar = content[i-1];

                        // Toggle inString state if we encounter a quote that is not escaped
                        if (char === '"' && prevChar !== '\\') {
                            inString = !inString;
                        }

                        // Only count brackets if we're not inside a string
                        if (!inString) {
                            if (char === '[') {
                                balance++;
                            } else if (char === ']') {
                                balance--;
                            }
                        }

                        // If balance is zero, we've found the end of our JSON object
                        if (balance === 0) {
                            endIndex = i + 1;
                            break;
                        }
                    }
                    
                    if (endIndex === -1) {
                        console.error('[Injector] Could not find the end of the JSON payload using bracket counting.');
                        continue;
                    }

                    const rawPayload = content.substring(payloadStartIndex, endIndex);
                    const jsonString = rawPayload.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                    
                    console.log('[Injector] Payload extracted via bracket counting. Parsing JSON...');
                    const parsedData = JSON.parse(jsonString);

                    function findAllInitialStates(obj) {
                        let allModels = [];
                        function search(currentObj) {
                            if (!currentObj || typeof currentObj !== 'object') return;
                            if (currentObj.hasOwnProperty('initialState') && Array.isArray(currentObj.initialState)) {
                                if (currentObj.initialState.length > 0 && currentObj.initialState[0] && currentObj.initialState[0].publicName) {
                                    allModels = allModels.concat(currentObj.initialState);
                                }
                            }
                            for (const key in currentObj) {
                                if (currentObj.hasOwnProperty(key)) search(currentObj[key]);
                            }
                        }
                        search(obj);
                        return allModels;
                    }

                    modelData = findAllInitialStates(parsedData);
                    if (modelData && modelData.length > 0) {
                        console.log(`[Injector] Successfully aggregated ${modelData.length} models.`);
                        break; // Exit after processing the correct script
                    }
                }
            }

            if (!modelData || modelData.length === 0) {
                console.warn('[Injector] Model extraction failed.');
                return null;
            }
            
            const registry = {};
            modelData.forEach(model => {
                if (!model || typeof model !== 'object' || !model.publicName) return;
                if (registry[model.publicName]) return;

                let type = 'chat';
                if (model.capabilities && model.capabilities.outputCapabilities) {
                    if (model.capabilities.outputCapabilities.image) type = 'image';
                    else if (model.capabilities.outputCapabilities.video) type = 'video';
                }
                
                registry[model.publicName] = { type: type, ...model };
            });
            
            console.log(`[Injector] ✅ Extracted ${Object.keys(registry).length} unique models.`);
            return registry;
            
        } catch (error) {
            console.error('[Injector] ❌ Error extracting model registry:', error);
            return null;
        }
    }
    
    function sendModelRegistry() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            console.log('[Injector] ⚠️ WebSocket not ready, cannot send model registry');
            return;
        }
        
        const models = extractModelRegistry();
        
        if (models && Object.keys(models).length > 0) {
            const message = {
                type: 'model_registry',
                models: models
            };
            
            socket.send(JSON.stringify(message));
            console.log(`[Injector] 📤 Sent model registry with ${Object.keys(models).length} models`);
        } else {
            console.warn('[Injector] ⚠️ No models extracted, not sending registry');
        }
    }

    // --- Start the connection ---
    connect();
    
    // Also try to send model registry when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(sendModelRegistry, 3000);
        });
    } else {
        setTimeout(sendModelRegistry, 3000);
    }

})();