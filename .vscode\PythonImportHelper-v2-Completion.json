[{"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "re", "kind": 6, "isExtraImport": true, "importPath": "re", "description": "re", "detail": "re", "documentation": {}}, {"label": "asynccontextmanager", "importPath": "contextlib", "description": "contextlib", "isExtraImport": true, "detail": "contextlib", "documentation": {}}, {"label": "asynccontextmanager", "importPath": "contextlib", "description": "contextlib", "isExtraImport": true, "detail": "contextlib", "documentation": {}}, {"label": "u<PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "u<PERSON><PERSON>", "description": "u<PERSON><PERSON>", "detail": "u<PERSON><PERSON>", "documentation": {}}, {"label": "FastAPI", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "WebSocket", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "WebSocketDisconnect", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Request", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "FastAPI", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "WebSocket", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "WebSocketDisconnect", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Request", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "StreamingResponse", "importPath": "starlette.responses", "description": "starlette.responses", "isExtraImport": true, "detail": "starlette.responses", "documentation": {}}, {"label": "StreamingResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "update_model_registry", "kind": 2, "importPath": "2lmarena_local", "description": "2lmarena_local", "peekOfCode": "def update_model_registry(models_data: dict) -> None:\n    \"\"\"Update the model registry with data from browser, inferring type from capabilities.\"\"\"\n    global MODEL_REGISTRY\n    try:\n        if not models_data or not isinstance(models_data, dict):\n            logging.warning(f\"Received empty or invalid model data: {models_data}\")\n            return\n        new_registry = {}\n        for public_name, model_info in models_data.items():\n            if not isinstance(model_info, dict):", "detail": "2lmarena_local", "documentation": {}}, {"label": "get_fallback_registry", "kind": 2, "importPath": "2lmarena_local", "description": "2lmarena_local", "peekOfCode": "def get_fallback_registry():\n    \"\"\"Fallback registry in case dynamic fetching fails.\"\"\"\n    return {\n        \"wolfstride\": {\"id\": \"849f6833-8598-4329-b7a6-e765eb2a0c0d\", \"type\": \"chat\"},\n        \"claude-3-5-sonnet-20241022\": {\"id\": \"f44e280a-7914-43ca-a25d-ecfcc5d48d09\", \"type\": \"chat\"},\n        \"gemini-2.0-flash-001\": {\"id\": \"7a55108b-b997-4cff-a72f-5aa83beee918\", \"type\": \"chat\"},\n        \"chatgpt-4o-latest-20250326\": {\"id\": \"9513524d-882e-4350-b31e-e4584440c2c8\", \"type\": \"chat\"},\n        \"o3-2025-04-16\": {\"id\": \"cb0f1e24-e8e9-4745-aabc-b926ffde7475\", \"type\": \"chat\"},\n        \"claude-opus-4-20250514\": {\"id\": \"ee116d12-64d6-48a8-88e5-b2d06325cdd2\", \"type\": \"chat\"},\n        \"claude-sonnet-4-20250514\": {\"id\": \"ac44dd10-0666-451c-b824-386ccfea7bcc\", \"type\": \"chat\"},", "detail": "2lmarena_local", "documentation": {}}, {"label": "create_lmarena_request_body", "kind": 2, "importPath": "2lmarena_local", "description": "2lmarena_local", "peekOfCode": "def create_lmarena_request_body(openai_req: dict) -> (dict, list):\n    model_name = openai_req[\"model\"]\n    if model_name not in MODEL_REGISTRY:\n        raise ValueError(f\"Model '{model_name}' not found in registry. Available models: {list(MODEL_REGISTRY.keys())}\")\n    model_info = MODEL_REGISTRY[model_name]\n    model_id = model_info.get(\"id\", model_name)\n    modality = model_info.get(\"type\", \"chat\")\n    evaluation_id = str(uuid.uuid4())\n    files_to_upload = []\n    processed_messages = []", "detail": "2lmarena_local", "documentation": {}}, {"label": "BACKPRESSURE_QUEUE_SIZE", "kind": 5, "importPath": "2lmarena_local", "description": "2lmarena_local", "peekOfCode": "BACKPRESSURE_QUEUE_SIZE = 50 # Increased size slightly\n# --- Model Registry ---\nMODEL_REGISTRY = {}  # Will be populated dynamically\ndef update_model_registry(models_data: dict) -> None:\n    \"\"\"Update the model registry with data from browser, inferring type from capabilities.\"\"\"\n    global MODEL_REGISTRY\n    try:\n        if not models_data or not isinstance(models_data, dict):\n            logging.warning(f\"Received empty or invalid model data: {models_data}\")\n            return", "detail": "2lmarena_local", "documentation": {}}, {"label": "MODEL_REGISTRY", "kind": 5, "importPath": "2lmarena_local", "description": "2lmarena_local", "peekOfCode": "MODEL_REGISTRY = {}  # Will be populated dynamically\ndef update_model_registry(models_data: dict) -> None:\n    \"\"\"Update the model registry with data from browser, inferring type from capabilities.\"\"\"\n    global MODEL_REGISTRY\n    try:\n        if not models_data or not isinstance(models_data, dict):\n            logging.warning(f\"Received empty or invalid model data: {models_data}\")\n            return\n        new_registry = {}\n        for public_name, model_info in models_data.items():", "detail": "2lmarena_local", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "2lmarena_local", "description": "2lmarena_local", "peekOfCode": "app = FastAPI(lifespan=lifespan)\n# --- WebSock<PERSON> Handler (Producer) ---\***************(\"/ws\")\nasync def websocket_endpoint(websocket: WebSocket):\n    global browser_ws\n    await websocket.accept()\n    logging.info(\"✅ WebSocket from browser connected.\")\n    browser_ws = websocket\n    try:\n        while True:", "detail": "2lmarena_local", "documentation": {}}, {"label": "create_lmarena_request_body", "kind": 2, "importPath": "lmarena local", "description": "lmarena local", "peekOfCode": "def create_lmarena_request_body(openai_req: dict) -> dict:\n    model_name = openai_req[\"model\"]\n    model_info = MODEL_REGISTRY[model_name]\n    model_id = model_info[\"id\"]\n    modality = model_info[\"type\"]\n    evaluation_id = str(uuid.uuid4())\n    arena_messages = []\n    message_ids = [str(uuid.uuid4()) for _ in openai_req['messages']]\n    for i, msg in enumerate(openai_req['messages']):\n        parent_message_ids = [message_ids[i-1]] if i > 0 else []", "detail": "lmarena local", "documentation": {}}, {"label": "MODEL_REGISTRY", "kind": 5, "importPath": "lmarena local", "description": "lmarena local", "peekOfCode": "MODEL_REGISTRY = {\n    \"wolfstride\": {\"id\": \"849f6833-8598-4329-b7a6-e765eb2a0c0d\", \"type\": \"chat\"},\n    \"claude-3-5-sonnet-20241022\": {\"id\": \"f44e280a-7914-43ca-a25d-ecfcc5d48d09\", \"type\": \"chat\"},\n    \"gemini-2.0-flash-001\": {\"id\": \"7a55108b-b997-4cff-a72f-5aa83beee918\", \"type\": \"chat\"},\n    \"chatgpt-4o-latest-20250326\": {\"id\": \"9513524d-882e-4350-b31e-e4584440c2c8\", \"type\": \"chat\"},\n    \"llama-4-maverick-03-26-experimental\": {\"id\": \"49bd7403-c7fd-4d91-9829-90a91906ad6c\", \"type\": \"chat\"},\n    \"gpt-4.1-2025-04-14\": {\"id\": \"14e9311c-94d2-40c2-8c54-273947e208b0\", \"type\": \"chat\"},\n    \"qwq-32b\": {\"id\": \"885976d3-d178-48f5-a3f4-6e13e0718872\", \"type\": \"chat\"},\n    \"grok-3-preview-02-24\": {\"id\": \"bd2c8278-af7a-4ec3-84db-0a426c785564\", \"type\": \"chat\"},\n    \"claude-3-7-sonnet-20250219-thinking-32k\": {\"id\": \"be98fcfd-345c-4ae1-9a82-a19123ebf1d2\", \"type\": \"chat\"},", "detail": "lmarena local", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "lmarena local", "description": "lmarena local", "peekOfCode": "app = FastAPI(lifespan=lifespan)\***************(\"/ws\")\nasync def websocket_endpoint(websocket: WebSocket):\n    \"\"\"\n    Handles the WebSocket connection from the browser and acts as a router,\n    dispatching incoming messages to the correct response channel.\n    \"\"\"\n    global browser_ws\n    await websocket.accept()\n    logging.info(\"✅ WebSocket connection from browser accepted.\")", "detail": "lmarena local", "documentation": {}}]