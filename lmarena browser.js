// ==UserScript==
// @name         New Userscript
// @namespace    http://tampermonkey.net/
// @version      2025-07-07
// @description  try to take over the world!
// <AUTHOR>
// @match        https://canary.lmarena.ai/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=discord.com
// @grant        none
// ==/UserScript==
(function () {
    'use strict';

    // --- Configuration ---
    const SERVER_URL = "ws://localhost:9080/ws";
    const TARGET_API_PATH = "/api/stream/create-evaluation";
    let socket;

    function connect() {
        console.log(`[Injector] Connecting to server at ${SERVER_URL}...`);
        socket = new WebSocket(SERVER_URL);

        socket.onopen = () => {
            console.log("[Injector] ✅ Connection established with local server.");
        };

        socket.onmessage = async (event) => {
            try {
                const message = JSON.parse(event.data);
                const { request_id, payload } = message;

                if (!request_id || !payload) {
                    console.error("[Injector] Invalid message from server:", message);
                    return;
                }

                console.log(`[Injector] ⬇️ Received request ${request_id}. Firing fetch.`);
                // Asynchronously execute the fetch and stream results back
                await executeFetchAndStreamBack(request_id, payload);

            } catch (error) {
                console.error("[Injector] Error processing message from server:", error);
            }
        };

        socket.onclose = () => {
            console.warn("[Injector] 🔌 Connection to local server closed. Retrying in 5 seconds...");
            setTimeout(connect, 5000);
        };

        socket.onerror = (error) => {
            console.error("[Injector] ❌ WebSocket error:", error);
            socket.close(); // This will trigger the onclose reconnect logic
        };
    }

    async function executeFetchAndStreamBack(requestId, payload) {
        try {
            const response = await fetch(`https://canary.lmarena.ai${TARGET_API_PATH}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/plain;charset=UTF-8',
                    'Accept': '*/*',
                    // The browser automatically adds critical headers:
                    // Cookie, User-Agent, sec-ch-ua, etc.
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok || !response.body) {
                const errorText = await response.text();
                throw new Error(`Fetch failed with status ${response.status}: ${errorText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    console.log(`[Injector] ✅ Stream finished for request ${requestId}.`);
                    sendToServer(requestId, "[DONE]");
                    break;
                }

                const chunk = decoder.decode(value);
                // The stream often sends multiple lines in one chunk
                const lines = chunk.split('\n').filter(line => line.trim() !== '');
                for (const line of lines) {
                    sendToServer(requestId, line);
                }
            }

        } catch (error) {
            console.error(`[Injector] ❌ Error during fetch for request ${requestId}:`, error);
            sendToServer(requestId, JSON.stringify({ error: error.message }));
        }
    }

    function sendToServer(requestId, data) {
        if (socket && socket.readyState === WebSocket.OPEN) {
            const message = {
                request_id: requestId,
                data: data
            };
            socket.send(JSON.stringify(message));
        } else {
            console.error("[Injector] Cannot send data, socket is not open.");
        }
    }

    // --- Start the connection ---
    connect();

})();